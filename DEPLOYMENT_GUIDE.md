# DeekshantWorks - Deployment Guide

This guide will help you deploy your React portfolio website to various hosting platforms. The project is built with Vite, React, TypeScript, and Tailwind CSS.

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn package manager
- Git (for version control)

### Local Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📦 Production Build

Before deploying, always create a production build:

```bash
npm run build
```

This creates a `dist` folder with optimized files ready for deployment.

## 🌐 Deployment Options

### 1. Vercel (Recommended)

**Why Vercel?**
- Zero configuration for Vite projects
- Automatic deployments from Git
- Global CDN
- Free tier available

**Steps:**
1. Push your code to GitHub/GitLab/Bitbucket
2. Go to [vercel.com](https://vercel.com) and sign up
3. Click "New Project" and import your repository
4. Vercel auto-detects Vite configuration
5. Click "Deploy"

**Custom Domain:**
- Go to Project Settings → Domains
- Add your custom domain
- Update DNS records as instructed

### 2. Netlify

**Steps:**
1. Build your project: `npm run build`
2. Go to [netlify.com](https://netlify.com) and sign up
3. Drag and drop the `dist` folder to Netlify
4. Or connect your Git repository for automatic deployments

**Configuration:**
Create `netlify.toml` in project root:
```toml
[build]
  publish = "dist"
  command = "npm run build"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 3. GitHub Pages

**Steps:**
1. Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build
      run: npm run build
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

2. Enable GitHub Pages in repository settings
3. Set source to "GitHub Actions"

### 4. Firebase Hosting

**Steps:**
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login`
3. Initialize: `firebase init hosting`
4. Configure:
   - Public directory: `dist`
   - Single-page app: `Yes`
   - Overwrite index.html: `No`
5. Build: `npm run build`
6. Deploy: `firebase deploy`

### 5. AWS S3 + CloudFront

**Steps:**
1. Create S3 bucket with static website hosting
2. Build project: `npm run build`
3. Upload `dist` contents to S3
4. Create CloudFront distribution
5. Configure custom domain (optional)

## 🔧 Configuration for Production

### Environment Variables
Create `.env.production` for production-specific variables:
```env
VITE_API_URL=https://your-api.com
VITE_ANALYTICS_ID=your-analytics-id
```

### Base URL Configuration
If deploying to a subdirectory, update `vite.config.ts`:
```typescript
export default defineConfig({
  base: '/your-subdirectory/',
  // ... other config
});
```

## 🛠️ Build Optimization

### Bundle Analysis
```bash
npm install --save-dev rollup-plugin-visualizer
```

Add to `vite.config.ts`:
```typescript
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    // ... other plugins
    visualizer({
      filename: 'dist/stats.html',
      open: true,
    }),
  ],
});
```

### Performance Tips
1. **Image Optimization**: Use WebP format for images
2. **Code Splitting**: Vite handles this automatically
3. **Tree Shaking**: Remove unused dependencies
4. **Compression**: Enable gzip/brotli on your server

## 🔍 Troubleshooting

### Common Issues

**1. Routing Issues (404 on refresh)**
- Add redirect rules for SPA routing
- Configure your hosting provider for client-side routing

**2. Build Failures**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm run build
```

**3. Environment Variables Not Working**
- Ensure variables start with `VITE_`
- Check `.env` file location (project root)

**4. Assets Not Loading**
- Check base URL configuration
- Verify asset paths are relative

### Performance Monitoring
- Use Lighthouse for performance audits
- Monitor Core Web Vitals
- Set up error tracking (Sentry, LogRocket)

## 📱 Mobile Optimization

Your site is already responsive, but ensure:
- Test on various devices
- Optimize images for mobile
- Check touch interactions
- Verify loading performance on slow networks

## 🔒 Security Headers

Add security headers via your hosting provider:
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'
X-Frame-Options: DENY
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
```

## 📊 Analytics Setup

Add Google Analytics or similar:
1. Get tracking ID
2. Add to environment variables
3. Implement tracking in your React app

## 🚀 Continuous Deployment

Set up automatic deployments:
1. Connect repository to hosting provider
2. Configure build settings
3. Set up branch protection rules
4. Enable automatic deployments on push

---

## 📞 Support

If you encounter issues:
1. Check the hosting provider's documentation
2. Review build logs for errors
3. Test locally with `npm run preview`
4. Check browser console for client-side errors

**Happy Deploying! 🎉**
