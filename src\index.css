
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 4%;
    --foreground: 0 0% 100%;
    --card: 0 0% 4%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 4%;
    --secondary: 0 0% 9%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 9%;
    --muted-foreground: 0 0% 60%;
    --accent: 0 0% 9%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;
    --radius: 0.5rem;
    --accent-color: #00ADB5;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-deekshant-black text-deekshant-white;
    font-family: 'Inter', sans-serif;
    cursor: none;
  }

  ::selection {
    @apply bg-deekshant-white text-deekshant-black;
  }

  html {
    @apply scroll-smooth;
  }
}

@layer components {
  /* Cursor Styles */
  .cursor-container {
    position: fixed;
    pointer-events: none;
    z-index: 10000;
    will-change: transform;
  }

  .cursor-dot {
    width: 8px;
    height: 8px;
    background-color: #ffffff;
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    mix-blend-mode: difference;
    z-index: 9999;
    transition: opacity 0.3s ease, width 0.3s ease, height 0.3s ease;
  }

  .cursor-outline {
    width: 40px;
    height: 40px;
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    position: fixed;
    pointer-events: none;
    z-index: 9998;
    mix-blend-mode: difference;
    transition: all 0.5s ease-out;
  }

  .cursor-dot.active {
    width: 16px;
    height: 16px;
    background-color: #00ADB5;
  }

  .cursor-outline.active {
    width: 60px;
    height: 60px;
    background-color: rgba(0, 173, 181, 0.1);
    border-color: #00ADB5;
    border-width: 2px;
  }

  .cursor-dot.clicking {
    transform: scale(0.7);
    background-color: #00ADB5;
  }

  .cursor-outline.clicking {
    transform: scale(0.7);
    background-color: rgba(0, 173, 181, 0.2);
  }

  /* Navigation Styles */
  .nav-link {
    @apply relative cursor-pointer overflow-hidden;
  }

  .nav-link::after {
    @apply absolute bottom-0 left-0 w-0 h-[2px] bg-[#00ADB5] content-[''] transition-all duration-300;
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  /* Glass Card Styling */
  .glass-card {
    @apply bg-gray-900/30 backdrop-blur-sm border border-gray-800/50 transition-all duration-300;
  }

  /* Underline Effects */
  .wavy-underline {
    @apply relative text-deekshant-teal;
  }

  .wavy-underline::after {
    content: '';
    position: absolute;
    left: -2px;
    right: -2px;
    bottom: 0;
    height: 2px;
    background: linear-gradient(90deg, #00ADB5, #00ADB5 50%, transparent 50%, transparent 100%);
    background-size: 8px 2px;
    opacity: 0.8;
    animation: wave-move 2s linear infinite;
  }

  .curly-underline {
    @apply relative inline-block text-white;
  }

  .curly-underline::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: -2px;
    height: 3px;
    background-color: #00ADB5;
    border-radius: 4px;
    transform: scaleX(1);
    opacity: 0.8;
    transition: transform 0.3s ease-in-out;
  }

  .curly-underline:hover::after {
    transform: scaleX(1.05);
    opacity: 1;
  }

  /* Project Item Styles */
  .project-item {
    @apply relative overflow-hidden transition-all duration-500;
  }

  .project-item img {
    @apply transition-all duration-700 ease-in-out;
  }

  .project-item:hover img {
    @apply scale-105 blur-sm;
  }

  .project-info {
    @apply absolute inset-0 flex flex-col justify-end p-6 bg-gradient-to-t from-deekshant-black/90 to-transparent opacity-0 transition-opacity duration-300;
  }

  .project-item:hover .project-info {
    @apply opacity-100;
  }

  /* Animation Classes */
  .animated-text span {
    display: inline-block;
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s forwards;
  }

  .animate-fade-in {
    animation: fadeIn 1s forwards;
  }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes wave-move {
    0% {
      background-position-x: 0;
    }
    100% {
      background-position-x: 16px;
    }
  }
}

@media (max-width: 768px) {
  body {
    cursor: auto;
  }

  .cursor-dot, .cursor-outline {
    display: none;
  }
}
